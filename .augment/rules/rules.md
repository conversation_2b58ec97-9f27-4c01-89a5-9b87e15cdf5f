---
type: "always_apply"
---

Create a Next.js 15 project with the following specifications:

**Backend Requirements:**

1. Create a REST API endpoint that:
   - Accepts two parameters: "username" and "action"
   - Stores these parameters in a database along with a timestamp of when the request was made
   - Returns appropriate success/error responses

**Frontend Requirements:**

1. Create a web page that displays:

   - All stored records (username, action, timestamp) in chronological order
   - UI styled like a WeChat chat interface with message bubbles
   - All text and labels in Chinese
   - Filtering capabilities by username, action, and timestamp

2. Design specifications:

   - Modern, beautiful, and clean UI design
   - Fully responsive layout supporting both desktop and mobile devices
   - Use appropriate spacing, typography, and color schemes

3. Security:
   - Implement password protection for the frontend page
   - Simple password-only authentication (no user registration/login system required)
   - Users should only need to enter a password once to access the page

**Technical Stack:**

- Framework: Next.js 15
- Database: MySQL
- Styling: tailwindcss
- Responsive design: Ensure mobile-first approach

**Deliverables:**

- Complete Next.js 15 application
- Database schema and setup
- API endpoint implementation
- Responsive frontend with chat-like interface
- Password protection mechanism
- Chinese localization for all UI text
